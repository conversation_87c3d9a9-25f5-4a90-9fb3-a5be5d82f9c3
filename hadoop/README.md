### 制作记录

这份helm-charts是根据这份docker-compose.yml翻译过来的，docker-compose的方式可以这样启动起来（机器先安装好docker和docker-compose）：
```shell
git clone https://github.com/big-data-europe/docker-hadoop.git
cp docker-hadoop/docker-compose.yml  docker-hadoop/hadoop.env .
docker-compose up -d
```

里面用到镜像的Dockerfile在：https://github.com/big-data-europe/docker-hadoop

在转换的过程中，主要碰到的问题是hostname找到的问题，经过多次调试之后，记录一些解决的问题点：

1) 每个服务都要搞一个headless service，这样statefulset才可以ping得到、同时pod也能ping得到；当时没有datanode的headless时，resourcemanager老是起不来。
2) hadoop-env中的`CORE_CONF_fs_defaultFS`，要写到pod级别的域名，不然提交job提交不上去，会有noRouteToHost的异常

说明：这个版本的job运行比较慢，试着用腾讯云大数据16核64G的机器，跑起来还是要18秒，所以不是机器配置的问题，原因不清楚。（暂不管了，仅学习用）

### 说明

1. 磁盘持久化还没做，这个优先级不高，这个主要还是用来做学习和实验的，不会用于生产环境部署。因此不做磁盘持久化了。

### 验证部署是否成功：

进入namenode执行（实际集群datanode/resourcemanager/nodemanager等任何一个节点都可以）：

```bash
## dfs验证
hdfs dfs -ls /
echo hi > hi.txt
hdfs dfs -put ./hi.txt  /hi.txt
hdfs dfs -cat /hi.txt
# 这里能打印出内容就算成功了
### 跑map reduce任务
yarn jar /opt/hadoop-3.2.1/share/hadoop/mapreduce/hadoop-mapreduce-examples-3.2.1.jar wordcount /hi.txt /output
hdfs dfs -cat /output/part-r-00000
# 这里能输出正确的单词个数就成功了
```
