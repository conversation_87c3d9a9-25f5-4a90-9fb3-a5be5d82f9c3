apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}
spec:
  selector:
    starrocks: {{ .Release.Name }}
  {{if .Values.nodePorts}}
  type: NodePort
  {{else}}
  type: ClusterIP
  {{end}}
  ports:
  - port: 9030
    name: fe-port
    targetPort: 9030
    {{if and .Values.nodePorts .Values.nodePorts.fe}}nodePort: {{ .Values.nodePorts.fe }}{{end}}
  - port: 8030
    name: be-port
    targetPort: 8030
    {{if and .Values.nodePorts .Values.nodePorts.be}}nodePort: {{ .Values.nodePorts.be }}{{end}}
  - port: 8040
    name: web-port
    targetPort: 8040
    {{if and .Values.nodePorts .Values.nodePorts.web}}nodePort: {{ .Values.nodePorts.web }}{{end}}
