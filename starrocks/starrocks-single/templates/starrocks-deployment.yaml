apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
spec:
  replicas: 1 # single node
  strategy:
    type: Recreate # k8s default is RollingUpdate, starrocks should stop old to keep data and then restart
  selector:
    matchLabels:
      starrocks: {{ .Release.Name }}
  template:
    metadata:
      labels:
        starrocks: {{ .Release.Name }}
    spec:
      containers:
      - name: starrocks
        image: {{ .Values.image }}
        ports:
        - containerPort: 9030
          name: fe-port
          {{if and .Values.hostPorts .Values.hostPorts.fe}}hostPort: {{ .Values.hostPorts.fe }}{{end}}
        - containerPort: 8030
          name: be-port
          {{if and .Values.hostPorts .Values.hostPorts.be}}hostPort: {{ .Values.hostPorts.be }}{{end}}
        - containerPort: 8040
          name: web-port
          {{if and .Values.hostPorts .Values.hostPorts.web}}hostPort: {{ .Values.hostPorts.web }}{{end}}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
