# docker image (required)
image: starrocks/allin1-ubuntu:3.3.15
# registry.cn-shenzhen.aliyuncs.com/dockerpub/allin1-ubuntu:3.3.15

# (optional) if hostPorts have values, starrocks ports will expose to host ports
# hostPorts:
#   fe: 30090  # Frontend port 9030
#   be: 30080  # Backend port 8030
#   web: 30040 # Web UI port 8040

# (optional) if nodePorts have values, starrocks ports will expose to node ports
# nodePorts:
#   fe: 30091  # Frontend port 9030
#   be: 30081  # Backend port 8030
#   web: 30041 # Web UI port 8040

# (optional) specific which node to schedule to
nodeSelector:
#   node/starrocks: "foobar"
